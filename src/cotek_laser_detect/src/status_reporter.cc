#include "status_reporter.h"
#include <chrono>
#include <jsoncpp/json/json.h>
#include <iomanip>
#include <sstream>

namespace cotek_laser_detect {

StatusReporter::StatusReporter(LaserStorageOption option) 
        : option_(option), thread_pool_(4) {
}

StatusReporter::~StatusReporter() {
  // Destructor implementation
}

template <typename T>
std::string StatusReporter::GetCurrentTime() {
  std::chrono::time_point<std::chrono::system_clock, std::chrono::milliseconds>
      tp = std::chrono::time_point_cast<std::chrono::milliseconds>(
          std::chrono::system_clock::now());
  auto tmp = std::chrono::duration_cast<std::chrono::milliseconds>(
      tp.time_since_epoch());
  return std::to_string(tmp.count());
}

// Explicit instantiation for the template function
template std::string StatusReporter::GetCurrentTime<std::string>();

std::string StatusReporter::PackStorageInfo(
    std::map<std::string, std::shared_ptr<cotek_laser_detect::Stock>> id_stocks) {
  Json::Value root;
  std::string current_time = GetFormattedDateTime();
  
  Json::Value storage(Json::arrayValue);

  for (const auto &area_obj : option_.area_stock_id_map) {
    Json::Value area;
    area["areaId"] = area_obj.first;
    area["stockList"] = Json::Value(Json::arrayValue);

    // 遍历当前区域的库存列表
    for (auto stock : area_obj.second) {
      Json::Value stock_json;

      // 如果找到对应的库存信息
      if (id_stocks.find(stock) != id_stocks.end()) {
        auto &stock_data = id_stocks[stock];
        StockState state = stock_data->get_state();
        
        stock_json["nodeId"] = stock_data->get_id();
        
        // 将状态简化为0未知，1占据，2空闲三种状态
        int reportState = 0; // 默认为0，表示未知
        if (state == StockState::FREE) {
          reportState = 2; // 空闲为2
        } else if (state == StockState::OCCUPY || state == StockState::HALF_FULL) {
          reportState = 1; // 占据(包括半笼和满笼)为1
        }
        stock_json["state"] = reportState;
        
        // 添加level字段，表示满笼/半笼状态
        int level = 0; // 默认为0，表示未知
        
        // 只有当库位配置为需要检测满/半笼时，才根据状态设置level
        if (stock_data->option().detect_fill_level) {
          if (state == StockState::OCCUPY) {
            level = 2; // 2表示满笼
          } else if (state == StockState::HALF_FULL) {
            level = 1; // 1表示半笼
          }
        }
        
        stock_json["level"] = level;
        
        // 添加height_score字段
        stock_json["heightScore"] = stock_data->get_height_score();
      } else {
        stock_json = Json::nullValue;
      }
      area["stockList"].append(stock_json);
    }

    storage.append(area);
  }

  root["time"] = current_time;
  root["storage"] = storage;

  // Replace deprecated StyledWriter with StreamWriterBuilder
  Json::StreamWriterBuilder writer;
  writer["indentation"] = "  ";  // Two space indentation
  std::string json_str = Json::writeString(writer, root);
  return json_str;
}

void StatusReporter::reportAllStockStatus(
    const std::map<std::string, std::shared_ptr<cotek_laser_detect::LaserDetector>> &detectors) {
  // 所有激光的 id_stocks
  std::map<std::string, std::shared_ptr<cotek_laser_detect::Stock>> all_id_stocks;
  for (const auto &detector_pair : detectors) {
    const auto &detector_id = detector_pair.first;
    const auto &detector = detector_pair.second;
    auto id_stocks = detector->GetCurrentStatus();
    all_id_stocks.insert(id_stocks.begin(), id_stocks.end());
  }
  
  // Generate the report
  std::string storage_info_string = PackStorageInfo(all_id_stocks);
  ROS_INFO_STREAM(storage_info_string);
  SendStorageInfo(storage_info_string);
}

void StatusReporter::SendStorageInfo(const std::string& storage_info_string) {
  // Create a copy of the string for the thread
  std::string info_copy = storage_info_string;
  
  // Store communication options as local copies for thread
  std::string server_ip = option_.communicate_option.server_ip;
  int server_port = option_.communicate_option.server_port;
  std::string server_api = option_.communicate_option.server_api;

  auto no_block_send = [server_ip, server_port, server_api, info_copy]() {
    try {
      // Record start time for response time measurement
      auto start_time = std::chrono::high_resolution_clock::now();
      
      // send to server using copied data
      util::RemoteServiceEntry entry(server_ip, server_port, server_api);
      bool result = util::RemoteService::PostString(entry, info_copy);
      
      // Record end time and calculate response time
      auto end_time = std::chrono::high_resolution_clock::now();
      auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
      long response_time_ms = duration.count();
      
      if (result) {
        ROS_INFO_STREAM("Storage info send succeed! Response time: " << response_time_ms << "ms");
      } else {
        ROS_ERROR_STREAM("Storage info send failed! Response time: " << response_time_ms << "ms");
      }
    } catch (std::out_of_range& ex) {
      ROS_ERROR_STREAM("Storage info send exception: " << ex.what());
    }
  };

  thread_pool_.enqueue(no_block_send);
}

std::string StatusReporter::GetFormattedDateTime() {
  auto now = std::chrono::system_clock::now();
  auto time_t_now = std::chrono::system_clock::to_time_t(now);
  
  std::ostringstream oss;
  oss << std::put_time(std::localtime(&time_t_now), "%Y-%m-%d %H:%M:%S");
  return oss.str();
}

void StatusReporter::reportLaserAliveStatus(
    const std::map<std::string, std::shared_ptr<cotek_laser_detect::LaserDetector>> &detectors) {
  Json::Value root;
  
  Json::Value radars(Json::arrayValue);

  for (const auto &detector_pair : detectors) {
    const auto &detector_id = detector_pair.first;
    const auto &detector = detector_pair.second;
    Json::Value radar;
    
    // Set required fields
    radar["name"] = detector_id;
    
    // IP address from configuration or default
    radar["ip"] = detector->GetOption().detect_option.count(detector_id) > 0 && 
                  !detector->GetOption().detect_option.at(detector_id).ip_address.empty() ? 
                  detector->GetOption().detect_option.at(detector_id).ip_address : "0.0.0.0";
    
    // Type 1 for laser radar
    radar["type"] = 1;
    
    // Status mapping: true->2(on), false->3(failed)
    radar["statu"] = detector->IsAlive() ? 2 : 3;

    if (!detector->IsAlive()){
      ROS_WARN_STREAM("Lidar: " << detector_id << " is dead !!!");
    }
    
    // Code field can be empty or use detector ID as code
    radar["code"] = detector_id;
    
    // Error status based on alive status
    radar["isError"] = !detector->IsAlive();
    
    // error info
    radar["errorInfo"] = detector->IsAlive() ? "no error" : "radar is dead";
    
    // Region from configuration or default
    radar["region"] = detector->GetOption().detect_option.count(detector_id) > 0 && 
                      !detector->GetOption().detect_option.at(detector_id).region.empty() ? 
                      detector->GetOption().detect_option.at(detector_id).region : "unknown";
    
    // Current formatted time
    radar["updateTime"] = GetFormattedDateTime();
    
    radars.append(radar);
  }

  root["radar"] = radars;

  // Replace deprecated StyledWriter with StreamWriterBuilder
  Json::StreamWriterBuilder writer;
  writer["indentation"] = "  ";  // Two space indentation
  std::string laser_status_string = Json::writeString(writer, root);
  // ROS_INFO_STREAM(laser_status_string);
  SendLaserAliveInfo(laser_status_string);
}

void StatusReporter::SendLaserAliveInfo(const std::string& laser_status_string) {
  // Create a copy of the string for the thread
  std::string info_copy = laser_status_string;
  
  // Store communication options as local copies for thread
  std::string server_ip = option_.communicate_option.server_ip;
  int server_port = option_.communicate_option.server_port;
  std::string laser_alive_api = option_.communicate_option.laser_alive_api;
  
  auto no_block_send = [server_ip, server_port, laser_alive_api, info_copy]() {
    try {
      // Record start time for response time measurement
      auto start_time = std::chrono::high_resolution_clock::now();
      
      // send to server using copied data
      util::RemoteServiceEntry entry(server_ip, server_port, laser_alive_api);
      bool result = util::RemoteService::PostString(entry, info_copy);
      
      // Record end time and calculate response time
      auto end_time = std::chrono::high_resolution_clock::now();
      auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
      long response_time_ms = duration.count();
      
      if (result) {
        ROS_INFO_STREAM("Laser alive status send succeed! Response time: " << response_time_ms << "ms");
      } else {
        ROS_ERROR_STREAM("Laser alive status send failed! Response time: " << response_time_ms << "ms");
      }
    } catch (std::out_of_range& ex) {
      ROS_ERROR_STREAM("Laser alive status send exception: " << ex.what());
    }
  };

  thread_pool_.enqueue(no_block_send);
}

} // namespace cotek_laser_detect 